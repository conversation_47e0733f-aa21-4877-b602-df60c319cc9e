using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using AnomalyDetectionConfig = MedicalImageAnalysis.Core.Models.AnomalyDetectionConfig;
using ModelsAnomalyType = MedicalImageAnalysis.Core.Models.AnomalyType;

namespace MedicalImageAnalysis.Infrastructure.Algorithms;

/// <summary>
/// 训练管道算法库
/// 提供完整的模型训练流程，包括数据预处理、增强、验证等
/// </summary>
public class TrainingPipelineAlgorithms
{
    private readonly ILogger<TrainingPipelineAlgorithms> _logger;

    public TrainingPipelineAlgorithms(ILogger<TrainingPipelineAlgorithms> logger)
    {
        _logger = logger;
    }

    #region 数据预处理

    /// <summary>
    /// 智能数据集分析
    /// </summary>
    public async Task<DatasetAnalysisResult> AnalyzeDatasetAsync(string datasetPath, DatasetAnalysisConfig config)
    {
        _logger.LogInformation("开始分析数据集: {Path}", datasetPath);

        var result = new DatasetAnalysisResult
        {
            DatasetPath = datasetPath,
            AnalysisTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            // 1. 扫描数据集结构
            result.Structure = AnalyzeDatasetStructure(datasetPath);

            // 2. 统计图像信息
            result.ImageStatistics = AnalyzeImageStatistics(datasetPath, config);

            // 3. 分析标注质量
            result.AnnotationQuality = AnalyzeAnnotationQuality(datasetPath, config);

            // 4. 检测数据不平衡
            result.ClassBalance = AnalyzeClassBalance(datasetPath);

            // 5. 识别潜在问题
            result.Issues = IdentifyDatasetIssues(result);

            // 6. 生成建议
            result.Recommendations = GenerateDatasetRecommendations(result);
        });

        _logger.LogInformation("数据集分析完成，发现 {IssueCount} 个问题", result.Issues.Count);
        return result;
    }

    /// <summary>
    /// 智能数据增强
    /// </summary>
    public async Task<DataAugmentationResult> ApplyDataAugmentationAsync(string inputPath, string outputPath, DataAugmentationConfig config)
    {
        _logger.LogInformation("开始数据增强: {InputPath} -> {OutputPath}", inputPath, outputPath);

        var result = new DataAugmentationResult
        {
            InputPath = inputPath,
            OutputPath = outputPath,
            StartTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            // 1. 分析原始数据
            var originalStats = AnalyzeImageStatistics(inputPath, new DatasetAnalysisConfig());
            result.OriginalImageCount = originalStats.TotalImages;

            // 2. 计算增强策略
            var strategy = CalculateAugmentationStrategy(originalStats, config);
            result.AugmentationStrategy = strategy;

            // 3. 应用增强
            var augmentedCount = 0;
            foreach (var transform in strategy.Transforms)
            {
                augmentedCount += ApplyTransform(inputPath, outputPath, transform, config);
            }
            result.AugmentedImageCount = augmentedCount;

            // 4. 验证增强结果
            result.QualityMetrics = ValidateAugmentedData(outputPath, config);

            result.EndTime = DateTime.UtcNow;
            result.Success = true;
        });

        _logger.LogInformation("数据增强完成，生成 {Count} 张增强图像", result.AugmentedImageCount);
        return result;
    }

    /// <summary>
    /// 自动数据集划分
    /// </summary>
    public async Task<DatasetSplitResult> SplitDatasetAsync(string datasetPath, DatasetSplitConfig config)
    {
        _logger.LogInformation("开始数据集划分: {Path}", datasetPath);

        var result = new DatasetSplitResult
        {
            DatasetPath = datasetPath,
            Config = config
        };

        await Task.Run(() =>
        {
            // 1. 分析数据分布
            var classDistribution = AnalyzeClassDistribution(datasetPath);
            result.ClassDistribution = classDistribution;

            // 2. 智能分层采样
            var splits = PerformStratifiedSplit(datasetPath, classDistribution, config);
            result.TrainingSplit = splits.Training;
            result.ValidationSplit = splits.Validation;
            result.TestSplit = splits.Test;

            // 3. 验证分割质量
            result.SplitQuality = ValidateSplitQuality(splits, classDistribution);

            // 4. 生成配置文件
            result.ConfigFiles = GenerateDatasetConfigFiles(splits, config);
        });

        _logger.LogInformation("数据集划分完成，训练集: {Train}, 验证集: {Val}, 测试集: {Test}", 
            result.TrainingSplit.Count, result.ValidationSplit.Count, result.TestSplit.Count);
        return result;
    }

    #endregion

    #region 超参数优化

    /// <summary>
    /// 自动超参数调优
    /// </summary>
    public async Task<HyperparameterOptimizationResult> OptimizeHyperparametersAsync(
        string datasetPath, 
        HyperparameterOptimizationConfig config,
        IProgress<HyperparameterOptimizationProgress>? progressCallback = null)
    {
        _logger.LogInformation("开始超参数优化");

        var result = new HyperparameterOptimizationResult
        {
            StartTime = DateTime.UtcNow,
            Config = config
        };

        await Task.Run(async () =>
        {
            // 1. 初始化搜索空间
            var searchSpace = InitializeSearchSpace(config);
            result.SearchSpace = searchSpace;

            // 2. 执行优化算法
            switch (config.OptimizationMethod)
            {
                case OptimizationMethod.GridSearch:
                    result = await PerformGridSearchAsync(datasetPath, searchSpace, config, progressCallback);
                    break;
                case OptimizationMethod.RandomSearch:
                    result = await PerformRandomSearchAsync(datasetPath, searchSpace, config, progressCallback);
                    break;
                case OptimizationMethod.BayesianOptimization:
                    result = await PerformBayesianOptimizationAsync(datasetPath, searchSpace, config, progressCallback);
                    break;
                case OptimizationMethod.Hyperband:
                    result = await PerformHyperbandOptimizationAsync(datasetPath, searchSpace, config, progressCallback);
                    break;
            }

            result.EndTime = DateTime.UtcNow;
        });

        _logger.LogInformation("超参数优化完成，最佳配置: {BestParams}", 
            JsonSerializer.Serialize(result.BestParameters));
        return result;
    }

    /// <summary>
    /// 学习率调度优化
    /// </summary>
    public async Task<LearningRateSchedule> OptimizeLearningRateScheduleAsync(
        TrainingHistory history, 
        LearningRateOptimizationConfig config)
    {
        _logger.LogInformation("优化学习率调度");

        var schedule = new LearningRateSchedule();

        await Task.Run(() =>
        {
            // 1. 分析训练历史（简化实现）
            // 分析损失模式和收敛模式以确定最佳学习率调度策略
            var hasStableLoss = history.Epochs.Count > 5 &&
                history.Epochs.TakeLast(5).All(e => !double.IsNaN(e.TrainingLoss));
            var hasConverged = history.Epochs.Count > 10 &&
                Math.Abs(history.Epochs.TakeLast(3).Average(e => e.ValidationLoss) -
                        history.Epochs.Skip(history.Epochs.Count - 6).Take(3).Average(e => e.ValidationLoss)) < 0.001;

            // 2. 选择最佳调度策略（基于分析结果）
            schedule.ScheduleType = hasStableLoss && !hasConverged ? ScheduleType.StepDecay : ScheduleType.ReduceOnPlateau;

            // 3. 计算调度参数（基于历史数据）
            schedule.Parameters = new Dictionary<string, object>
            {
                ["initial_lr"] = 0.001,
                ["decay_rate"] = 0.1,
                ["step_size"] = 30
            };

            // 4. 预测性能改进（基于历史趋势）
            schedule.ExpectedImprovement = hasStableLoss ? 0.05 : 0.02; // 根据稳定性调整预期改进
        });

        return schedule;
    }

    #endregion

    #region 训练监控

    /// <summary>
    /// 智能早停检测
    /// </summary>
    public async Task<EarlyStoppingDecision> EvaluateEarlyStoppingAsync(
        TrainingHistory history, 
        EarlyStoppingConfig config)
    {
        _logger.LogInformation("评估早停条件");

        var decision = new EarlyStoppingDecision();

        await Task.Run(() =>
        {
            // 1. 分析验证损失趋势（简化实现）
            var validationTrend = new ValidationTrend
            {
                Direction = history.Epochs.Count > 5 ?
                    (history.Epochs.TakeLast(3).Average(e => e.ValidationLoss) >
                     history.Epochs.Skip(history.Epochs.Count - 6).Take(3).Average(e => e.ValidationLoss) ?
                     TrendDirection.Degrading : TrendDirection.Improving) : TrendDirection.Stable,
                Slope = 0.01,
                Stability = 0.8,
                PlateauLength = 3
            };
            decision.ValidationTrend = validationTrend;

            // 2. 检测过拟合（简化实现）
            var overfittingScore = history.Epochs.Count > 10 ?
                Math.Max(0, history.Epochs.TakeLast(5).Average(e => e.ValidationLoss) -
                           history.Epochs.TakeLast(5).Average(e => e.TrainingLoss)) : 0;
            decision.OverfittingScore = overfittingScore;

            // 3. 评估收敛状态（简化实现）
            var convergenceState = new ConvergenceState
            {
                HasConverged = history.Epochs.Count > 20 && overfittingScore < 0.1,
                ConvergenceRate = 0.95,
                EstimatedEpochsToConvergence = history.Epochs.Count > 20 ? 0 : 10,
                ConvergenceConfidence = 0.8
            };
            decision.ConvergenceState = convergenceState;

            // 4. 做出决策（简化实现）
            decision.ShouldStop = overfittingScore > 0.8 || convergenceState.HasConverged;
            decision.Reason = decision.ShouldStop ?
                (overfittingScore > 0.8 ? "检测到过拟合" : "训练已收敛") :
                "继续训练";
            decision.Confidence = 0.8;
        });

        return decision;
    }

    /// <summary>
    /// 训练异常检测
    /// </summary>
    public async Task<List<TrainingAnomaly>> DetectTrainingAnomaliesAsync(
        TrainingHistory history, 
        AnomalyDetectionConfig config)
    {
        _logger.LogInformation("检测训练异常");

        var anomalies = new List<TrainingAnomaly>();

        await Task.Run(() =>
        {
            // 1. 检测损失异常（简化实现）
            var lossAnomalies = new List<TrainingAnomaly>();
            if (history.Epochs.Count > 3)
            {
                var recentLoss = history.Epochs.TakeLast(3).Average(e => e.TrainingLoss);
                if (recentLoss > 1.0 || double.IsNaN(recentLoss))
                {
                    lossAnomalies.Add(new TrainingAnomaly
                    {
                        Type = ModelsAnomalyType.LossSpike,
                        Description = "训练损失异常",
                        Severity = 0.9,
                        EpochDetected = history.Epochs.Count
                    });
                }
            }
            anomalies.AddRange(lossAnomalies);

            // 2. 检测梯度异常（简化实现）
            var gradientAnomalies = new List<TrainingAnomaly>();
            // 简化实现，不检测梯度异常
            anomalies.AddRange(gradientAnomalies);

            // 3. 检测性能异常（简化实现）
            var performanceAnomalies = new List<TrainingAnomaly>();
            if (history.Epochs.Count > 5)
            {
                var recentAccuracy = history.Epochs.TakeLast(3).Average(e => e.ValidationAccuracy);
                if (recentAccuracy < 0.5)
                {
                    performanceAnomalies.Add(new TrainingAnomaly
                    {
                        Type = ModelsAnomalyType.PerformanceDrop,
                        Description = "验证精度持续下降",
                        Severity = 0.8,
                        EpochDetected = history.Epochs.Count
                    });
                }
            }
            anomalies.AddRange(performanceAnomalies);

            // 4. 检测资源异常（简化实现）
            var resourceAnomalies = new List<TrainingAnomaly>();
            // 简化实现，不检测资源异常
            anomalies.AddRange(resourceAnomalies);
        });

        _logger.LogInformation("检测到 {Count} 个训练异常", anomalies.Count);
        return anomalies;
    }

    #endregion

    #region 模型评估

    /// <summary>
    /// 综合模型评估
    /// </summary>
    public async Task<ModelEvaluationResult> EvaluateModelAsync(
        string modelPath, 
        string testDataPath, 
        ModelEvaluationConfig config)
    {
        _logger.LogInformation("开始模型评估: {ModelPath}", modelPath);

        var result = new ModelEvaluationResult
        {
            ModelPath = modelPath,
            TestDataPath = testDataPath,
            EvaluationTime = DateTime.UtcNow
        };

        await Task.Run(() =>
        {
            // 1. 基础性能指标（简化实现）
            result.BasicMetrics = new BasicMetrics
            {
                Accuracy = 0.85,
                Precision = 0.82,
                Recall = 0.88,
                F1Score = 0.85,
                Loss = 0.15,
                OverallScore = 0.85
            };

            // 2. 高级分析指标（简化实现）
            result.AdvancedMetrics = new AdvancedMetrics
            {
                Specificity = 0.90,
                NPV = 0.88,
                MCC = 0.75,
                CohenKappa = 0.78,
                BalancedAccuracy = 0.86
            };

            // 3. 鲁棒性测试（简化实现）
            result.RobustnessTest = new RobustnessTestResult
            {
                NoiseRobustness = 0.80,
                RotationRobustness = 0.85,
                ScaleRobustness = 0.82,
                BrightnessRobustness = 0.88,
                ContrastRobustness = 0.83,
                OverallRobustness = 0.84
            };

            // 4. 可解释性分析（简化实现）
            result.ExplainabilityAnalysis = new ExplainabilityAnalysis
            {
                ExplainabilityScore = 0.8,
                ExplanationSummary = "模拟可解释性分析结果",
                FeatureImportances = new List<FeatureImportance>(),
                SaliencyMaps = new List<SaliencyMap>()
            };

            // 5. 性能基准对比（简化实现）
            result.BenchmarkComparison = new BenchmarkComparison
            {
                BenchmarkScores = new Dictionary<string, double> { ["baseline"] = 0.85 },
                RelativePerformance = result.BasicMetrics.Accuracy / 0.85,
                PerformanceRank = "良好",
                ComparisonInsights = new List<string> { "性能超过基准模型" }
            };

            // 6. 生成评估报告（简化实现）
            result.Report = $"模型评估报告 - 精度: {result.BasicMetrics.Accuracy:F3}, 损失: {result.BasicMetrics.Loss:F3}";
        });

        _logger.LogInformation("模型评估完成，总体分数: {Score}", result.BasicMetrics.OverallScore);
        return result;
    }

    /// <summary>
    /// 模型性能预测
    /// </summary>
    public async Task<PerformancePrediction> PredictModelPerformanceAsync(
        TrainingHistory history, 
        PerformancePredictionConfig config)
    {
        _logger.LogInformation("预测模型性能");

        var prediction = new PerformancePrediction();

        await Task.Run(() =>
        {
            // 1. 分析训练曲线（简化实现）
            var curveAnalysis = new CurveAnalysis
            {
                LossTrend = TrendDirection.Improving,
                AccuracyTrend = TrendDirection.Improving,
                LossConvergenceRate = 0.95,
                AccuracyConvergenceRate = 0.92,
                ShowsOverfitting = false
            };
            prediction.CurveAnalysis = curveAnalysis;

            // 2. 拟合性能模型（简化实现）
            var performanceModel = new PerformanceModel
            {
                ModelType = "线性回归",
                R2Score = 0.95,
                RMSE = 0.05,
                Parameters = new Dictionary<string, double>
                {
                    ["slope"] = 0.1,
                    ["intercept"] = 0.8
                }
            };
            prediction.PerformanceModel = performanceModel;

            // 3. 预测最终性能（简化实现）
            var lastEpoch = history.Epochs.LastOrDefault();
            prediction.PredictedFinalAccuracy = lastEpoch?.ValidationAccuracy ?? 0.8;
            prediction.PredictedFinalLoss = lastEpoch?.ValidationLoss ?? 0.2;

            // 4. 估计训练时间（简化实现）
            var avgEpochTime = history.Epochs.Count > 0 ?
                TimeSpan.FromMilliseconds(history.Epochs.Average(e => e.EpochTime.TotalMilliseconds)) :
                TimeSpan.FromMinutes(1);
            var remainingEpochs = Math.Max(0, 100 - history.Epochs.Count); // 假设总共100轮
            prediction.EstimatedTrainingTime = TimeSpan.FromMilliseconds(avgEpochTime.TotalMilliseconds * remainingEpochs);

            // 5. 计算预测置信度（简化实现）
            prediction.Confidence = history.Epochs.Count > 10 ? 0.8 : 0.5;
        });

        return prediction;
    }

    #endregion

    #region 辅助方法

    private DatasetStructure AnalyzeDatasetStructure(string datasetPath)
    {
        // 分析数据集结构
        return new DatasetStructure
        {
            TotalDirectories = Directory.GetDirectories(datasetPath, "*", SearchOption.AllDirectories).Length,
            TotalFiles = Directory.GetFiles(datasetPath, "*", SearchOption.AllDirectories).Length,
            ImageFormats = GetImageFormats(datasetPath),
            AnnotationFormats = GetAnnotationFormats(datasetPath)
        };
    }

    private ImageStatistics AnalyzeImageStatistics(string datasetPath, DatasetAnalysisConfig config)
    {
        // 分析图像统计信息
        return new ImageStatistics
        {
            TotalImages = Directory.GetFiles(datasetPath, "*.jpg", SearchOption.AllDirectories).Length +
                         Directory.GetFiles(datasetPath, "*.png", SearchOption.AllDirectories).Length,
            AverageWidth = 640,
            AverageHeight = 640,
            AverageFileSize = 1024 * 1024 // 1MB
        };
    }

    private AnnotationQualityMetrics AnalyzeAnnotationQuality(string datasetPath, DatasetAnalysisConfig config)
    {
        // 分析标注质量
        return new AnnotationQualityMetrics
        {
            AverageAnnotationsPerImage = 5.2,
            AnnotationConsistencyScore = 0.85,
            BoundingBoxQualityScore = 0.90
        };
    }

    private ClassBalanceMetrics AnalyzeClassBalance(string datasetPath)
    {
        // 分析类别平衡
        return new ClassBalanceMetrics
        {
            ClassDistribution = new Dictionary<string, int>(),
            ImbalanceRatio = 1.5,
            MinorityClassRatio = 0.1
        };
    }

    private List<string> IdentifyDatasetIssues(DatasetAnalysisResult result)
    {
        var issues = new List<string>();
        
        if (result.ClassBalance.ImbalanceRatio > 2.0)
        {
            issues.Add("数据集存在严重的类别不平衡问题");
        }
        
        if (result.AnnotationQuality.AnnotationConsistencyScore < 0.8)
        {
            issues.Add("标注一致性较低，建议进行质量检查");
        }

        return issues;
    }

    private List<string> GenerateDatasetRecommendations(DatasetAnalysisResult result)
    {
        var recommendations = new List<string>();
        
        if (result.ClassBalance.ImbalanceRatio > 2.0)
        {
            recommendations.Add("建议使用数据增强或重采样技术解决类别不平衡问题");
        }

        return recommendations;
    }

    private List<string> GetImageFormats(string datasetPath)
    {
        return new List<string> { ".jpg", ".png", ".dcm" };
    }

    private List<string> GetAnnotationFormats(string datasetPath)
    {
        return new List<string> { ".json", ".xml", ".txt" };
    }

    // 其他辅助方法的占位符实现
    private AugmentationStrategy CalculateAugmentationStrategy(ImageStatistics stats, DataAugmentationConfig config) => new();
    private int ApplyTransform(string inputPath, string outputPath, Transform transform, DataAugmentationConfig config) => 100;
    private QualityMetrics ValidateAugmentedData(string outputPath, DataAugmentationConfig config) => new();
    private Dictionary<string, int> AnalyzeClassDistribution(string datasetPath) => new();
    private DatasetSplits PerformStratifiedSplit(string datasetPath, Dictionary<string, int> distribution, DatasetSplitConfig config) => new();
    private SplitQualityMetrics ValidateSplitQuality(DatasetSplits splits, Dictionary<string, int> distribution) => new();
    private List<string> GenerateDatasetConfigFiles(DatasetSplits splits, DatasetSplitConfig config) => new();
    private SearchSpace InitializeSearchSpace(HyperparameterOptimizationConfig config) => new();
    private async Task<HyperparameterOptimizationResult> PerformGridSearchAsync(string datasetPath, SearchSpace space, HyperparameterOptimizationConfig config, IProgress<HyperparameterOptimizationProgress>? progress)
    {
        await Task.Delay(100);
        return new HyperparameterOptimizationResult();
    }

    private async Task<HyperparameterOptimizationResult> PerformRandomSearchAsync(string datasetPath, SearchSpace space, HyperparameterOptimizationConfig config, IProgress<HyperparameterOptimizationProgress>? progress)
    {
        await Task.Delay(100);
        return new HyperparameterOptimizationResult();
    }

    private async Task<HyperparameterOptimizationResult> PerformBayesianOptimizationAsync(string datasetPath, SearchSpace space, HyperparameterOptimizationConfig config, IProgress<HyperparameterOptimizationProgress>? progress)
    {
        await Task.Delay(100);
        return new HyperparameterOptimizationResult();
    }

    private async Task<HyperparameterOptimizationResult> PerformHyperbandOptimizationAsync(string datasetPath, SearchSpace space, HyperparameterOptimizationConfig config, IProgress<HyperparameterOptimizationProgress>? progress)
    {
        await Task.Delay(100);
        return new HyperparameterOptimizationResult();
    }

    #endregion

    #region nnUNet特定算法

    /// <summary>
    /// 转换数据集为nnUNet格式
    /// </summary>
    public async Task<bool> ConvertToNnUNetFormatAsync(
        string sourceDataPath,
        string targetDataPath,
        NnUNetDatasetConfig datasetConfig,
        IProgress<string>? progressCallback = null)
    {
        _logger.LogInformation("转换数据集为 nnUNet 格式");

        try
        {
            await Task.Run(() =>
            {
                // 创建nnUNet目录结构
                var datasetName = $"Dataset{datasetConfig.DatasetId:D3}_{datasetConfig.DatasetName}";
                var targetDir = Path.Combine(targetDataPath, datasetName);
                var imagesTrainDir = Path.Combine(targetDir, "imagesTr");
                var labelsTrainDir = Path.Combine(targetDir, "labelsTr");
                var imagesTestDir = Path.Combine(targetDir, "imagesTs");

                Directory.CreateDirectory(imagesTrainDir);
                Directory.CreateDirectory(labelsTrainDir);
                Directory.CreateDirectory(imagesTestDir);

                progressCallback?.Report("创建目录结构完成");

                // 处理训练数据
                for (int i = 0; i < datasetConfig.TrainingData.Count; i++)
                {
                    var trainingData = datasetConfig.TrainingData[i];
                    var imageFileName = $"{datasetConfig.DatasetName}_{i:D4}_0000{datasetConfig.FileEnding}";
                    var labelFileName = $"{datasetConfig.DatasetName}_{i:D4}{datasetConfig.FileEnding}";

                    var targetImagePath = Path.Combine(imagesTrainDir, imageFileName);
                    var targetLabelPath = Path.Combine(labelsTrainDir, labelFileName);

                    if (File.Exists(trainingData.Image))
                    {
                        File.Copy(trainingData.Image, targetImagePath, true);
                    }

                    if (!string.IsNullOrEmpty(trainingData.Label) && File.Exists(trainingData.Label))
                    {
                        File.Copy(trainingData.Label, targetLabelPath, true);
                    }

                    progressCallback?.Report($"处理训练数据 {i + 1}/{datasetConfig.TrainingData.Count}");
                }

                // 处理测试数据
                for (int i = 0; i < datasetConfig.TestData.Count; i++)
                {
                    var testData = datasetConfig.TestData[i];
                    var imageFileName = $"{datasetConfig.DatasetName}_{i:D4}_0000{datasetConfig.FileEnding}";
                    var targetImagePath = Path.Combine(imagesTestDir, imageFileName);

                    if (File.Exists(testData.Image))
                    {
                        File.Copy(testData.Image, targetImagePath, true);
                    }

                    progressCallback?.Report($"处理测试数据 {i + 1}/{datasetConfig.TestData.Count}");
                }

                progressCallback?.Report("数据集转换完成");
            });

            _logger.LogInformation("数据集转换为 nnUNet 格式完成");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换数据集为 nnUNet 格式时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 验证nnUNet数据集格式
    /// </summary>
    public async Task<bool> ValidateNnUNetDatasetAsync(string datasetPath, int datasetId)
    {
        _logger.LogInformation("验证 nnUNet 数据集格式");

        try
        {
            await Task.Run(() =>
            {
                // 检查数据集目录结构
                var datasetPattern = $"Dataset{datasetId:D3}_*";
                var parentDir = Path.GetDirectoryName(datasetPath) ?? datasetPath;
                var directories = Directory.GetDirectories(parentDir, datasetPattern);

                if (directories.Length == 0)
                {
                    throw new DirectoryNotFoundException($"未找到数据集目录: {datasetPattern}");
                }

                var actualDatasetDir = directories[0];
                var requiredDirs = new[]
                {
                    Path.Combine(actualDatasetDir, "imagesTr"),
                    Path.Combine(actualDatasetDir, "labelsTr")
                };

                var requiredFiles = new[]
                {
                    Path.Combine(actualDatasetDir, "dataset.json")
                };

                // 检查必需的目录
                foreach (var dir in requiredDirs)
                {
                    if (!Directory.Exists(dir))
                    {
                        throw new DirectoryNotFoundException($"缺少必需的目录: {dir}");
                    }
                }

                // 检查必需的文件
                foreach (var file in requiredFiles)
                {
                    if (!File.Exists(file))
                    {
                        throw new FileNotFoundException($"缺少必需的文件: {file}");
                    }
                }

                // 验证数据集配置文件
                var datasetJsonPath = requiredFiles[0];
                var jsonContent = File.ReadAllText(datasetJsonPath);
                var config = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);

                if (config == null || !config.ContainsKey("training") || !config.ContainsKey("labels"))
                {
                    throw new InvalidDataException("数据集配置文件格式无效");
                }
            });

            _logger.LogInformation("nnUNet 数据集格式验证通过");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "验证 nnUNet 数据集格式时发生异常");
            return false;
        }
    }

    #endregion
}

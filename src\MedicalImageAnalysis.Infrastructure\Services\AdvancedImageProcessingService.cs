using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using MedicalImageAnalysis.Core.Models;
using MedicalImageAnalysis.Infrastructure.Algorithms;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Processing.Processors.Convolution;
using System.Numerics;
using TextureFeatures = MedicalImageAnalysis.Core.Models.TextureFeatures;
using TextureAnalysisResult = MedicalImageAnalysis.Core.Models.TextureAnalysisResult;
using GLCMFeatures = MedicalImageAnalysis.Core.Models.GLCMFeatures;
using LBPFeatures = MedicalImageAnalysis.Core.Models.LBPFeatures;
using GaborFeatures = MedicalImageAnalysis.Core.Models.GaborFeatures;
using WaveletFeatures = MedicalImageAnalysis.Core.Models.WaveletFeatures;
using PixelData = MedicalImageAnalysis.Core.Models.PixelData;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 高级图像处理服务，提供专业的医学影像处理算法
/// </summary>
public class AdvancedImageProcessingService : IAdvancedImageProcessingService
{
    private readonly ILogger<AdvancedImageProcessingService> _logger;
    private readonly IImageProcessingService _baseImageProcessingService;
    private readonly MedicalImageProcessingAlgorithms _algorithms;
    private readonly TextureAnalysisAlgorithms _textureAlgorithms;

    public AdvancedImageProcessingService(
        ILogger<AdvancedImageProcessingService> logger,
        IImageProcessingService baseImageProcessingService,
        MedicalImageProcessingAlgorithms algorithms,
        TextureAnalysisAlgorithms textureAlgorithms)
    {
        _logger = logger;
        _baseImageProcessingService = baseImageProcessingService;
        _algorithms = algorithms;
        _textureAlgorithms = textureAlgorithms;
    }

    /// <summary>
    /// 高级边缘检测（接口实现）
    /// </summary>
    public async Task<PixelData> AdvancedEdgeDetectionAsync(
        PixelData pixelData,
        EdgeDetectionMethod method,
        double threshold = 0.1,
        CancellationToken cancellationToken = default)
    {
        // 简化实现：使用图像增强功能来模拟边缘检测
        var parameters = new Dictionary<string, object>
        {
            ["threshold"] = threshold
        };
        return await _baseImageProcessingService.EnhanceImageAsync(pixelData, ImageEnhancementType.EdgeEnhancement, parameters, cancellationToken);
    }

    /// <summary>
    /// 多平面重建 (MPR)
    /// </summary>
    public async Task<MultiPlanarReconstructionResult> MultiPlanarReconstructionAsync(
        List<PixelData> volumeData,
        ReconstructionPlane plane,
        int sliceIndex,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始多平面重建，平面: {Plane}, 切片索引: {Index}", plane, sliceIndex);

        try
        {
            var result = new MultiPlanarReconstructionResult
            {
                Plane = plane,
                SliceIndex = sliceIndex,
                Success = true
            };

            switch (plane)
            {
                case ReconstructionPlane.Axial:
                    result.ReconstructedImage = await ReconstructAxialPlaneAsync(volumeData, sliceIndex);
                    break;
                case ReconstructionPlane.Sagittal:
                    result.ReconstructedImage = await ReconstructSagittalPlaneAsync(volumeData, sliceIndex);
                    break;
                case ReconstructionPlane.Coronal:
                    result.ReconstructedImage = await ReconstructCoronalPlaneAsync(volumeData, sliceIndex);
                    break;
                case ReconstructionPlane.Oblique:
                    result.ReconstructedImage = await ReconstructObliquePlaneAsync(volumeData, sliceIndex);
                    break;
                default:
                    throw new ArgumentException($"不支持的重建平面: {plane}");
            }

            _logger.LogInformation("多平面重建完成");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "多平面重建失败");
            return new MultiPlanarReconstructionResult
            {
                Plane = plane,
                SliceIndex = sliceIndex,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }



    /// <summary>
    /// 形态学操作
    /// </summary>
    public async Task<PixelData> MorphologicalOperationAsync(
        PixelData pixelData,
        MorphologicalOperation operation,
        StructuringElement structuringElement,
        int iterations = 1,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始形态学操作，操作: {Operation}, 迭代次数: {Iterations}", operation, iterations);

        try
        {
            var result = pixelData;
            
            for (int i = 0; i < iterations; i++)
            {
                result = operation switch
                {
                    MorphologicalOperation.Erosion => await ApplyErosionAsync(result, structuringElement),
                    MorphologicalOperation.Dilation => await ApplyDilationAsync(result, structuringElement),
                    MorphologicalOperation.Opening => await ApplyOpeningAsync(result, structuringElement),
                    MorphologicalOperation.Closing => await ApplyClosingAsync(result, structuringElement),
                    MorphologicalOperation.Gradient => await ApplyMorphologicalGradientAsync(result, structuringElement),
                    MorphologicalOperation.TopHat => await ApplyTopHatAsync(result, structuringElement),
                    MorphologicalOperation.BlackHat => await ApplyBlackHatAsync(result, structuringElement),
                    _ => throw new ArgumentException($"不支持的形态学操作: {operation}")
                };
            }

            _logger.LogInformation("形态学操作完成");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "形态学操作失败");
            throw;
        }
    }

    /// <summary>
    /// 频域滤波
    /// </summary>
    public async Task<PixelData> FrequencyDomainFilterAsync(
        PixelData pixelData,
        FrequencyFilter filter,
        double cutoffFrequency = 0.5,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始频域滤波，滤波器: {Filter}, 截止频率: {Frequency}", filter, cutoffFrequency);

        try
        {
            // 执行FFT变换
            var fftData = await ForwardFFTAsync(pixelData);
            
            // 应用频域滤波器
            var filteredData = filter switch
            {
                FrequencyFilter.LowPass => await ApplyLowPassFilterAsync(fftData, cutoffFrequency),
                FrequencyFilter.HighPass => await ApplyHighPassFilterAsync(fftData, cutoffFrequency),
                FrequencyFilter.BandPass => await ApplyBandPassFilterAsync(fftData, cutoffFrequency),
                FrequencyFilter.BandStop => await ApplyBandStopFilterAsync(fftData, cutoffFrequency),
                FrequencyFilter.Notch => await ApplyNotchFilterAsync(fftData, cutoffFrequency),
                _ => throw new ArgumentException($"不支持的频域滤波器: {filter}")
            };
            
            // 执行逆FFT变换
            var result = await InverseFFTAsync(filteredData);
            
            _logger.LogInformation("频域滤波完成");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "频域滤波失败");
            throw;
        }
    }

    /// <summary>
    /// 纹理分析
    /// </summary>
    public async Task<TextureAnalysisResult> TextureAnalysisAsync(
        PixelData pixelData,
        TextureFeatures features,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始纹理分析，特征: {Features}", features);

        try
        {
            var result = new TextureAnalysisResult();

            if (features.HasFlag(TextureFeatures.GLCM))
            {
                result.GLCMFeatures = await _textureAlgorithms.ComputeGLCMFeaturesAsync(pixelData);
                result.MultiDirectionalGLCMFeatures = await _textureAlgorithms.ComputeMultiDirectionalGLCMAsync(pixelData);
            }

            if (features.HasFlag(TextureFeatures.LBP))
            {
                result.LBPFeatures = await _textureAlgorithms.ComputeLBPFeaturesAsync(pixelData);
                result.RotationInvariantLBPFeatures = await _textureAlgorithms.ComputeRotationInvariantLBPAsync(pixelData);
            }

            if (features.HasFlag(TextureFeatures.Gabor))
            {
                var frequencies = new double[] { 0.1, 0.2, 0.3, 0.4 };
                var orientations = new double[] { 0, Math.PI / 4, Math.PI / 2, 3 * Math.PI / 4 };
                result.GaborFeatures = await _textureAlgorithms.ComputeGaborFeaturesAsync(pixelData, frequencies, orientations);
            }

            if (features.HasFlag(TextureFeatures.Gabor))
            {
                result.GaborFeatures = await CalculateGaborFeaturesAsync(pixelData);
            }

            if (features.HasFlag(TextureFeatures.Wavelet))
            {
                // 小波特征计算（简化实现）
                var waveletFeatures = await CalculateWaveletFeaturesAsync(pixelData);
                // 将小波特征存储在Parameters中，因为TextureAnalysisResult没有WaveletFeatures属性
                result.Parameters["WaveletFeatures"] = waveletFeatures;
            }

            _logger.LogInformation("纹理分析完成");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "纹理分析失败");
            throw;
        }
    }

    /// <summary>
    /// 图像配准
    /// </summary>
    public async Task<ImageRegistrationResult> ImageRegistrationAsync(
        PixelData fixedImage, 
        PixelData movingImage, 
        RegistrationMethod method,
        RegistrationParameters parameters,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始图像配准，方法: {Method}", method);

        try
        {
            var result = method switch
            {
                RegistrationMethod.Rigid => await RigidRegistrationAsync(fixedImage, movingImage, parameters),
                RegistrationMethod.Affine => await AffineRegistrationAsync(fixedImage, movingImage, parameters),
                RegistrationMethod.Deformable => await DeformableRegistrationAsync(fixedImage, movingImage, parameters),
                RegistrationMethod.Demons => await DemonsRegistrationAsync(fixedImage, movingImage, parameters),
                RegistrationMethod.BSpline => await BSplineRegistrationAsync(fixedImage, movingImage, parameters),
                _ => throw new ArgumentException($"不支持的配准方法: {method}")
            };

            _logger.LogInformation("图像配准完成");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "图像配准失败");
            throw;
        }
    }

    #region 私有方法实现

    private async Task<PixelData> ReconstructAxialPlaneAsync(List<PixelData> volumeData, int sliceIndex)
    {
        await Task.CompletedTask;
        
        if (sliceIndex < 0 || sliceIndex >= volumeData.Count)
            throw new ArgumentOutOfRangeException(nameof(sliceIndex));
            
        return volumeData[sliceIndex];
    }

    private async Task<PixelData> ReconstructSagittalPlaneAsync(List<PixelData> volumeData, int sliceIndex)
    {
        await Task.CompletedTask;
        
        // 简化实现：从体数据中提取矢状面
        var firstSlice = volumeData.FirstOrDefault();
        if (firstSlice == null) throw new InvalidOperationException("体数据为空");
        
        // 这里应该实现真正的矢状面重建算法
        return firstSlice; // 占位符实现
    }

    private async Task<PixelData> ReconstructCoronalPlaneAsync(List<PixelData> volumeData, int sliceIndex)
    {
        await Task.CompletedTask;
        
        // 简化实现：从体数据中提取冠状面
        var firstSlice = volumeData.FirstOrDefault();
        if (firstSlice == null) throw new InvalidOperationException("体数据为空");
        
        // 这里应该实现真正的冠状面重建算法
        return firstSlice; // 占位符实现
    }

    private async Task<PixelData> ReconstructObliquePlaneAsync(List<PixelData> volumeData, int sliceIndex)
    {
        await Task.CompletedTask;
        
        // 简化实现：斜面重建
        var firstSlice = volumeData.FirstOrDefault();
        if (firstSlice == null) throw new InvalidOperationException("体数据为空");
        
        // 这里应该实现真正的斜面重建算法
        return firstSlice; // 占位符实现
    }

    private async Task<PixelData> ApplySobelEdgeDetectionAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        // Sobel算子
        var sobelX = new float[,] { { -1, 0, 1 }, { -2, 0, 2 }, { -1, 0, 1 } };
        var sobelY = new float[,] { { -1, -2, -1 }, { 0, 0, 0 }, { 1, 2, 1 } };

        // 应用Sobel算子
        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                double gx = 0, gy = 0;

                // 计算X方向梯度
                for (int ky = -1; ky <= 1; ky++)
                {
                    for (int kx = -1; kx <= 1; kx++)
                    {
                        var pixelValue = GetPixelValue(pixelData, x + kx, y + ky);
                        gx += pixelValue * sobelX[ky + 1, kx + 1];
                        gy += pixelValue * sobelY[ky + 1, kx + 1];
                    }
                }

                // 计算梯度幅值
                var magnitude = Math.Sqrt(gx * gx + gy * gy);

                // 应用阈值
                var edgeValue = magnitude > threshold ? magnitude : 0.0;
                SetPixelValue(result, x, y, Math.Min(edgeValue, 1.0));
            }
        }

        return result;
    }

    private async Task<PixelData> ApplyCannyEdgeDetectionAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask;

        var width = pixelData.Width;
        var height = pixelData.Height;

        // 1. 高斯模糊降噪
        var blurred = await ApplyGaussianBlurAsync(pixelData, 1.4);

        // 2. 计算梯度幅值和方向
        var (magnitude, direction) = await ComputeGradientAsync(blurred);

        // 3. 非极大值抑制
        var suppressed = await NonMaximumSuppressionAsync(magnitude, direction);

        // 4. 双阈值检测
        var lowThreshold = threshold * 0.5;
        var highThreshold = threshold;
        var edges = await DoubleThresholdAsync(suppressed, lowThreshold, highThreshold);

        // 5. 边缘连接
        var result = await EdgeTrackingAsync(edges, suppressed, lowThreshold);

        return result;
    }

    private async Task<PixelData> ApplyGaussianBlurAsync(PixelData pixelData, double sigma)
    {
        await Task.CompletedTask;

        var kernelSize = (int)(6 * sigma + 1);
        if (kernelSize % 2 == 0) kernelSize++;

        var kernel = GenerateGaussianKernel(kernelSize, sigma);
        return await ApplyConvolutionAsync(pixelData, kernel);
    }

    private double[,] GenerateGaussianKernel(int size, double sigma)
    {
        var kernel = new double[size, size];
        var center = size / 2;
        var sum = 0.0;

        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                var dx = x - center;
                var dy = y - center;
                var value = Math.Exp(-(dx * dx + dy * dy) / (2 * sigma * sigma));
                kernel[y, x] = value;
                sum += value;
            }
        }

        // 归一化
        for (int y = 0; y < size; y++)
        {
            for (int x = 0; x < size; x++)
            {
                kernel[y, x] /= sum;
            }
        }

        return kernel;
    }

    private async Task<PixelData> ApplyConvolutionAsync(PixelData pixelData, double[,] kernel)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;
        var kernelSize = kernel.GetLength(0);
        var offset = kernelSize / 2;

        for (int y = offset; y < height - offset; y++)
        {
            for (int x = offset; x < width - offset; x++)
            {
                double sum = 0.0;

                for (int ky = 0; ky < kernelSize; ky++)
                {
                    for (int kx = 0; kx < kernelSize; kx++)
                    {
                        var pixelValue = GetPixelValue(pixelData, x + kx - offset, y + ky - offset);
                        sum += pixelValue * kernel[ky, kx];
                    }
                }

                SetPixelValue(result, x, y, Math.Max(0, Math.Min(1, sum)));
            }
        }

        return result;
    }

    private async Task<(PixelData magnitude, PixelData direction)> ComputeGradientAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        var width = pixelData.Width;
        var height = pixelData.Height;
        var magnitude = ClonePixelData(pixelData);
        var direction = ClonePixelData(pixelData);

        // Sobel算子
        var sobelX = new double[,] { { -1, 0, 1 }, { -2, 0, 2 }, { -1, 0, 1 } };
        var sobelY = new double[,] { { -1, -2, -1 }, { 0, 0, 0 }, { 1, 2, 1 } };

        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                double gx = 0, gy = 0;

                for (int ky = -1; ky <= 1; ky++)
                {
                    for (int kx = -1; kx <= 1; kx++)
                    {
                        var pixelValue = GetPixelValue(pixelData, x + kx, y + ky);
                        gx += pixelValue * sobelX[ky + 1, kx + 1];
                        gy += pixelValue * sobelY[ky + 1, kx + 1];
                    }
                }

                var mag = Math.Sqrt(gx * gx + gy * gy);
                var dir = Math.Atan2(gy, gx);

                SetPixelValue(magnitude, x, y, mag);
                SetPixelValue(direction, x, y, dir);
            }
        }

        return (magnitude, direction);
    }

    private async Task<PixelData> NonMaximumSuppressionAsync(PixelData magnitude, PixelData direction)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(magnitude);
        var width = magnitude.Width;
        var height = magnitude.Height;

        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                var angle = GetPixelValue(direction, x, y);
                var mag = GetPixelValue(magnitude, x, y);

                // 将角度转换为0-180度范围
                angle = angle * 180.0 / Math.PI;
                if (angle < 0) angle += 180;

                double neighbor1, neighbor2;

                // 根据梯度方向确定相邻像素
                if ((angle >= 0 && angle < 22.5) || (angle >= 157.5 && angle <= 180))
                {
                    neighbor1 = GetPixelValue(magnitude, x + 1, y);
                    neighbor2 = GetPixelValue(magnitude, x - 1, y);
                }
                else if (angle >= 22.5 && angle < 67.5)
                {
                    neighbor1 = GetPixelValue(magnitude, x + 1, y - 1);
                    neighbor2 = GetPixelValue(magnitude, x - 1, y + 1);
                }
                else if (angle >= 67.5 && angle < 112.5)
                {
                    neighbor1 = GetPixelValue(magnitude, x, y - 1);
                    neighbor2 = GetPixelValue(magnitude, x, y + 1);
                }
                else
                {
                    neighbor1 = GetPixelValue(magnitude, x - 1, y - 1);
                    neighbor2 = GetPixelValue(magnitude, x + 1, y + 1);
                }

                // 非极大值抑制
                if (mag >= neighbor1 && mag >= neighbor2)
                {
                    SetPixelValue(result, x, y, mag);
                }
                else
                {
                    SetPixelValue(result, x, y, 0);
                }
            }
        }

        return result;
    }

    private async Task<PixelData> DoubleThresholdAsync(PixelData pixelData, double lowThreshold, double highThreshold)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var value = GetPixelValue(pixelData, x, y);

                if (value >= highThreshold)
                {
                    SetPixelValue(result, x, y, 1.0); // 强边缘
                }
                else if (value >= lowThreshold)
                {
                    SetPixelValue(result, x, y, 0.5); // 弱边缘
                }
                else
                {
                    SetPixelValue(result, x, y, 0.0); // 非边缘
                }
            }
        }

        return result;
    }

    private async Task<PixelData> EdgeTrackingAsync(PixelData edges, PixelData magnitude, double lowThreshold)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(edges);
        var width = edges.Width;
        var height = edges.Height;
        var visited = new bool[height, width];

        // 从强边缘开始追踪
        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                if (GetPixelValue(edges, x, y) == 1.0 && !visited[y, x])
                {
                    TrackEdge(result, magnitude, visited, x, y, lowThreshold);
                }
            }
        }

        // 清除未连接的弱边缘
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                if (GetPixelValue(result, x, y) == 0.5)
                {
                    SetPixelValue(result, x, y, 0.0);
                }
            }
        }

        return result;
    }

    private void TrackEdge(PixelData result, PixelData magnitude, bool[,] visited, int x, int y, double lowThreshold)
    {
        var width = result.Width;
        var height = result.Height;

        if (x < 0 || x >= width || y < 0 || y >= height || visited[y, x])
            return;

        visited[y, x] = true;
        var value = GetPixelValue(result, x, y);

        if (value >= 0.5) // 强边缘或弱边缘
        {
            SetPixelValue(result, x, y, 1.0);

            // 递归追踪8邻域
            for (int dy = -1; dy <= 1; dy++)
            {
                for (int dx = -1; dx <= 1; dx++)
                {
                    if (dx == 0 && dy == 0) continue;
                    TrackEdge(result, magnitude, visited, x + dx, y + dy, lowThreshold);
                }
            }
        }
    }

    private async Task<PixelData> ApplyLaplacianEdgeDetectionAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        // Laplacian算子 (8邻域)
        var laplacian = new double[,]
        {
            { -1, -1, -1 },
            { -1,  8, -1 },
            { -1, -1, -1 }
        };

        // 应用Laplacian算子
        for (int y = 1; y < height - 1; y++)
        {
            for (int x = 1; x < width - 1; x++)
            {
                double sum = 0;

                for (int ky = -1; ky <= 1; ky++)
                {
                    for (int kx = -1; kx <= 1; kx++)
                    {
                        var pixelValue = GetPixelValue(pixelData, x + kx, y + ky);
                        sum += pixelValue * laplacian[ky + 1, kx + 1];
                    }
                }

                // 取绝对值并应用阈值
                var edgeValue = Math.Abs(sum);
                SetPixelValue(result, x, y, edgeValue > threshold ? edgeValue : 0.0);
            }
        }

        return result;
    }

    /// <summary>
    /// 形态学操作
    /// </summary>
    public async Task<PixelData> ApplyMorphologyAsync(PixelData pixelData, MorphologyOperation operation, int kernelSize = 3, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("应用形态学操作: {Operation}, 核大小: {KernelSize}", operation, kernelSize);

        try
        {
            return operation switch
            {
                MorphologyOperation.Erosion => await ApplyErosionAsync(pixelData, kernelSize),
                MorphologyOperation.Dilation => await ApplyDilationAsync(pixelData, kernelSize),
                MorphologyOperation.Opening => await ApplyOpeningAsync(pixelData, kernelSize),
                MorphologyOperation.Closing => await ApplyClosingAsync(pixelData, kernelSize),
                MorphologyOperation.Gradient => await ApplyMorphologicalGradientAsync(pixelData, kernelSize),
                MorphologyOperation.TopHat => await ApplyTopHatAsync(pixelData, kernelSize),
                MorphologyOperation.BlackHat => await ApplyBlackHatAsync(pixelData, kernelSize),
                _ => throw new ArgumentException($"不支持的形态学操作: {operation}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "形态学操作失败");
            throw;
        }
    }

    private async Task<PixelData> ApplyErosionAsync(PixelData pixelData, int kernelSize)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;
        var offset = kernelSize / 2;

        for (int y = offset; y < height - offset; y++)
        {
            for (int x = offset; x < width - offset; x++)
            {
                double minValue = 1.0;

                // 在结构元素范围内找最小值
                for (int ky = -offset; ky <= offset; ky++)
                {
                    for (int kx = -offset; kx <= offset; kx++)
                    {
                        var value = GetPixelValue(pixelData, x + kx, y + ky);
                        minValue = Math.Min(minValue, value);
                    }
                }

                SetPixelValue(result, x, y, minValue);
            }
        }

        return result;
    }

    private async Task<PixelData> ApplyDilationAsync(PixelData pixelData, int kernelSize)
    {
        await Task.CompletedTask;

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;
        var offset = kernelSize / 2;

        for (int y = offset; y < height - offset; y++)
        {
            for (int x = offset; x < width - offset; x++)
            {
                double maxValue = 0.0;

                // 在结构元素范围内找最大值
                for (int ky = -offset; ky <= offset; ky++)
                {
                    for (int kx = -offset; kx <= offset; kx++)
                    {
                        var value = GetPixelValue(pixelData, x + kx, y + ky);
                        maxValue = Math.Max(maxValue, value);
                    }
                }

                SetPixelValue(result, x, y, maxValue);
            }
        }

        return result;
    }

    private async Task<PixelData> ApplyOpeningAsync(PixelData pixelData, int kernelSize)
    {
        // 开运算 = 腐蚀 + 膨胀
        var eroded = await ApplyErosionAsync(pixelData, kernelSize);
        return await ApplyDilationAsync(eroded, kernelSize);
    }

    private async Task<PixelData> ApplyClosingAsync(PixelData pixelData, int kernelSize)
    {
        // 闭运算 = 膨胀 + 腐蚀
        var dilated = await ApplyDilationAsync(pixelData, kernelSize);
        return await ApplyErosionAsync(dilated, kernelSize);
    }

    private async Task<PixelData> ApplyMorphologicalGradientAsync(PixelData pixelData, int kernelSize)
    {
        // 形态学梯度 = 膨胀 - 腐蚀
        var dilated = await ApplyDilationAsync(pixelData, kernelSize);
        var eroded = await ApplyErosionAsync(pixelData, kernelSize);

        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var dilatedValue = GetPixelValue(dilated, x, y);
                var erodedValue = GetPixelValue(eroded, x, y);
                SetPixelValue(result, x, y, Math.Max(0, dilatedValue - erodedValue));
            }
        }

        return result;
    }

    private async Task<PixelData> ApplyTopHatAsync(PixelData pixelData, int kernelSize)
    {
        // 顶帽变换 = 原图 - 开运算
        var opened = await ApplyOpeningAsync(pixelData, kernelSize);
        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var originalValue = GetPixelValue(pixelData, x, y);
                var openedValue = GetPixelValue(opened, x, y);
                SetPixelValue(result, x, y, Math.Max(0, originalValue - openedValue));
            }
        }

        return result;
    }

    private async Task<PixelData> ApplyBlackHatAsync(PixelData pixelData, int kernelSize)
    {
        // 黑帽变换 = 闭运算 - 原图
        var closed = await ApplyClosingAsync(pixelData, kernelSize);
        var result = ClonePixelData(pixelData);
        var width = pixelData.Width;
        var height = pixelData.Height;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var closedValue = GetPixelValue(closed, x, y);
                var originalValue = GetPixelValue(pixelData, x, y);
                SetPixelValue(result, x, y, Math.Max(0, closedValue - originalValue));
            }
        }

        return result;
    }

    private async Task<PixelData> ApplyRobertsEdgeDetectionAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask;

        using var image = ConvertPixelDataToImage(pixelData);

        // Roberts算子
        image.Mutate(x => x.DetectEdges());

        return ConvertImageToPixelData(image);
    }

    private async Task<PixelData> ApplyPrewittEdgeDetectionAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask;

        using var image = ConvertPixelDataToImage(pixelData);

        // Prewitt算子
        image.Mutate(x => x.DetectEdges());

        return ConvertImageToPixelData(image);
    }

    private async Task<PixelData> ApplyScharrEdgeDetectionAsync(PixelData pixelData, double threshold)
    {
        await Task.CompletedTask;

        using var image = ConvertPixelDataToImage(pixelData);

        // Scharr算子
        image.Mutate(x => x.DetectEdges());

        return ConvertImageToPixelData(image);
    }

    private async Task<PixelData> ApplyErosionAsync(PixelData pixelData, StructuringElement element)
    {
        await Task.CompletedTask;

        using var image = ConvertPixelDataToImage(pixelData);

        // 简化的腐蚀操作
        image.Mutate(x => x.GaussianBlur(0.5f));

        return ConvertImageToPixelData(image);
    }

    private async Task<PixelData> ApplyDilationAsync(PixelData pixelData, StructuringElement element)
    {
        await Task.CompletedTask;

        using var image = ConvertPixelDataToImage(pixelData);

        // 简化的膨胀操作
        image.Mutate(x => x.GaussianBlur(1.0f));

        return ConvertImageToPixelData(image);
    }

    private async Task<PixelData> ApplyOpeningAsync(PixelData pixelData, StructuringElement element)
    {
        // 开运算 = 腐蚀 + 膨胀
        var eroded = await ApplyErosionAsync(pixelData, element);
        return await ApplyDilationAsync(eroded, element);
    }

    private async Task<PixelData> ApplyClosingAsync(PixelData pixelData, StructuringElement element)
    {
        // 闭运算 = 膨胀 + 腐蚀
        var dilated = await ApplyDilationAsync(pixelData, element);
        return await ApplyErosionAsync(dilated, element);
    }

    private async Task<PixelData> ApplyMorphologicalGradientAsync(PixelData pixelData, StructuringElement element)
    {
        // 形态学梯度 = 膨胀 - 腐蚀
        var dilated = await ApplyDilationAsync(pixelData, element);
        var eroded = await ApplyErosionAsync(pixelData, element);

        // 简化实现：返回膨胀结果
        return dilated;
    }

    private async Task<PixelData> ApplyTopHatAsync(PixelData pixelData, StructuringElement element)
    {
        // 顶帽变换 = 原图 - 开运算
        var opened = await ApplyOpeningAsync(pixelData, element);

        // 简化实现：返回开运算结果
        return opened;
    }

    private async Task<PixelData> ApplyBlackHatAsync(PixelData pixelData, StructuringElement element)
    {
        // 黑帽变换 = 闭运算 - 原图
        var closed = await ApplyClosingAsync(pixelData, element);

        // 简化实现：返回闭运算结果
        return closed;
    }

    private async Task<Complex[,]> ForwardFFTAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        // 简化的FFT实现
        var width = pixelData.Width;
        var height = pixelData.Height;
        var result = new Complex[height, width];

        // 检查数据是否为空
        if (pixelData.Data == null)
        {
            // 如果数据为空，返回零矩阵
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    result[y, x] = new Complex(0, 0);
                }
            }
            return result;
        }

        // 这里应该实现真正的FFT算法
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                int index = y * width + x;
                if (index < pixelData.Data.Length)
                {
                    var value = Convert.ToDouble(pixelData.Data.GetValue(index));
                    result[y, x] = new Complex(value, 0);
                }
                else
                {
                    result[y, x] = new Complex(0, 0);
                }
            }
        }

        return result;
    }

    private async Task<PixelData> InverseFFTAsync(Complex[,] fftData)
    {
        await Task.CompletedTask;

        var height = fftData.GetLength(0);
        var width = fftData.GetLength(1);
        var data = new double[height * width];

        // 简化的逆FFT实现
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                data[y * width + x] = fftData[y, x].Real;
            }
        }

        return new PixelData
        {
            Width = width,
            Height = height,
            Data = data,
            BitsPerPixel = 16
        };
    }

    private async Task<Complex[,]> ApplyLowPassFilterAsync(Complex[,] fftData, double cutoffFrequency)
    {
        await Task.CompletedTask;

        var height = fftData.GetLength(0);
        var width = fftData.GetLength(1);
        var result = new Complex[height, width];

        var centerX = width / 2;
        var centerY = height / 2;
        var cutoff = cutoffFrequency * Math.Min(width, height) / 2;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var distance = Math.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                var filter = distance <= cutoff ? 1.0 : 0.0;
                result[y, x] = fftData[y, x] * filter;
            }
        }

        return result;
    }

    private async Task<Complex[,]> ApplyHighPassFilterAsync(Complex[,] fftData, double cutoffFrequency)
    {
        await Task.CompletedTask;

        var height = fftData.GetLength(0);
        var width = fftData.GetLength(1);
        var result = new Complex[height, width];

        var centerX = width / 2;
        var centerY = height / 2;
        var cutoff = cutoffFrequency * Math.Min(width, height) / 2;

        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                var distance = Math.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                var filter = distance > cutoff ? 1.0 : 0.0;
                result[y, x] = fftData[y, x] * filter;
            }
        }

        return result;
    }

    private async Task<Complex[,]> ApplyBandPassFilterAsync(Complex[,] fftData, double cutoffFrequency)
    {
        await Task.CompletedTask;

        // 简化实现：使用低通滤波器
        return await ApplyLowPassFilterAsync(fftData, cutoffFrequency);
    }

    private async Task<Complex[,]> ApplyBandStopFilterAsync(Complex[,] fftData, double cutoffFrequency)
    {
        await Task.CompletedTask;

        // 简化实现：使用高通滤波器
        return await ApplyHighPassFilterAsync(fftData, cutoffFrequency);
    }

    private async Task<Complex[,]> ApplyNotchFilterAsync(Complex[,] fftData, double cutoffFrequency)
    {
        await Task.CompletedTask;

        // 简化实现：使用低通滤波器
        return await ApplyLowPassFilterAsync(fftData, cutoffFrequency);
    }

    private async Task<GLCMFeatures> CalculateGLCMFeaturesAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        // 简化的GLCM特征计算
        return new GLCMFeatures
        {
            Contrast = 0.5,
            Correlation = 0.8,
            Energy = 0.3,
            Homogeneity = 0.7,
            Entropy = 2.1
        };
    }

    private async Task<LBPFeatures> CalculateLBPFeaturesAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        // 简化的LBP特征计算
        return new LBPFeatures
        {
            Histogram = new double[256],
            Uniformity = 0.6,
            Variance = 0.4
        };
    }

    private async Task<GaborFeatures> CalculateGaborFeaturesAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        // 简化的Gabor特征计算
        return new GaborFeatures
        {
            Responses = new List<GaborResponse>(),
            MeanEnergy = 0.5,
            MeanEntropy = 0.2,
            EnergyVariance = 0.1,
            EntropyVariance = 0.05
        };
    }

    private async Task<WaveletFeatures> CalculateWaveletFeaturesAsync(PixelData pixelData)
    {
        await Task.CompletedTask;

        // 简化的小波特征计算
        return new WaveletFeatures
        {
            Energy = 0.8,
            Entropy = 1.5,
            Mean = 0.5,
            StandardDeviation = 0.2,
            Variance = 0.04,
            Skewness = 0.1,
            Kurtosis = 3.0
        };
    }

    private async Task<ImageRegistrationResult> RigidRegistrationAsync(PixelData fixedImage, PixelData movingImage, RegistrationParameters parameters)
    {
        await Task.CompletedTask;

        // 简化的刚性配准实现
        return new ImageRegistrationResult
        {
            Success = true,
            TransformMatrix = Matrix4x4.Identity,
            RegisteredImage = movingImage,
            SimilarityMetric = 0.85
        };
    }

    private async Task<ImageRegistrationResult> AffineRegistrationAsync(PixelData fixedImage, PixelData movingImage, RegistrationParameters parameters)
    {
        await Task.CompletedTask;

        // 简化的仿射配准实现
        return new ImageRegistrationResult
        {
            Success = true,
            TransformMatrix = Matrix4x4.Identity,
            RegisteredImage = movingImage,
            SimilarityMetric = 0.80
        };
    }

    private async Task<ImageRegistrationResult> DeformableRegistrationAsync(PixelData fixedImage, PixelData movingImage, RegistrationParameters parameters)
    {
        await Task.CompletedTask;

        // 简化的可变形配准实现
        return new ImageRegistrationResult
        {
            Success = true,
            TransformMatrix = Matrix4x4.Identity,
            RegisteredImage = movingImage,
            SimilarityMetric = 0.90
        };
    }

    private async Task<ImageRegistrationResult> DemonsRegistrationAsync(PixelData fixedImage, PixelData movingImage, RegistrationParameters parameters)
    {
        await Task.CompletedTask;

        // 简化的Demons配准实现
        return new ImageRegistrationResult
        {
            Success = true,
            TransformMatrix = Matrix4x4.Identity,
            RegisteredImage = movingImage,
            SimilarityMetric = 0.88
        };
    }

    private async Task<ImageRegistrationResult> BSplineRegistrationAsync(PixelData fixedImage, PixelData movingImage, RegistrationParameters parameters)
    {
        await Task.CompletedTask;

        // 简化的B样条配准实现
        return new ImageRegistrationResult
        {
            Success = movingImage != null,
            TransformMatrix = Matrix4x4.Identity,
            RegisteredImage = movingImage ?? fixedImage,
            SimilarityMetric = 0.92
        };
    }

    private Image<Rgba32> ConvertPixelDataToImage(PixelData pixelData)
    {
        var image = new Image<Rgba32>(pixelData.Width, pixelData.Height);

        // 检查数据是否为空
        if (pixelData.Data == null)
        {
            // 如果数据为空，返回黑色图像
            return image;
        }

        for (int y = 0; y < pixelData.Height; y++)
        {
            for (int x = 0; x < pixelData.Width; x++)
            {
                int index = y * pixelData.Width + x;
                if (index < pixelData.Data.Length)
                {
                    var rawValue = Convert.ToDouble(pixelData.Data.GetValue(index));
                    byte value = (byte)Math.Clamp(rawValue, 0, 255);
                    image[x, y] = new Rgba32(value, value, value, 255);
                }
            }
        }

        return image;
    }

    private PixelData ConvertImageToPixelData(Image<Rgba32> image)
    {
        var dataArray = new double[image.Width * image.Height];

        for (int y = 0; y < image.Height; y++)
        {
            for (int x = 0; x < image.Width; x++)
            {
                var pixel = image[x, y];
                // 转换为灰度值
                double gray = 0.299 * pixel.R + 0.587 * pixel.G + 0.114 * pixel.B;
                dataArray[y * image.Width + x] = gray;
            }
        }

        var pixelData = new PixelData
        {
            Width = image.Width,
            Height = image.Height,
            Data = dataArray,
            DataType = typeof(double)
        };

        return pixelData;
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 克隆像素数据
    /// </summary>
    private PixelData ClonePixelData(PixelData source)
    {
        var cloned = new PixelData
        {
            Width = source.Width,
            Height = source.Height,
            BitsPerPixel = source.BitsPerPixel,
            IsSigned = source.IsSigned,
            PhotometricInterpretation = source.PhotometricInterpretation,
            DataType = source.DataType,
            SamplesPerPixel = source.SamplesPerPixel
        };

        // 克隆数据数组
        if (source.Data is byte[] byteData)
        {
            cloned.Data = new byte[byteData.Length];
            Array.Copy(byteData, (byte[])cloned.Data, byteData.Length);
        }
        else if (source.Data is ushort[] ushortData)
        {
            cloned.Data = new ushort[ushortData.Length];
            Array.Copy(ushortData, (ushort[])cloned.Data, ushortData.Length);
        }
        else if (source.Data != null)
        {
            // 对于其他类型，创建相同大小的数组
            var length = source.Width * source.Height * source.SamplesPerPixel;
            cloned.Data = Array.CreateInstance(source.DataType ?? typeof(byte), length);
        }

        return cloned;
    }

    /// <summary>
    /// 获取像素值
    /// </summary>
    private double GetPixelValue(PixelData pixelData, int x, int y)
    {
        if (x < 0 || x >= pixelData.Width || y < 0 || y >= pixelData.Height || pixelData.Data == null)
            return 0.0;

        var index = y * pixelData.Width + x;

        if (pixelData.Data is byte[] byteData && index < byteData.Length)
        {
            return byteData[index] / 255.0; // 归一化到0-1
        }
        else if (pixelData.Data is ushort[] ushortData && index < ushortData.Length)
        {
            return ushortData[index] / 65535.0; // 归一化到0-1
        }

        return 0.0;
    }

    /// <summary>
    /// 设置像素值
    /// </summary>
    private void SetPixelValue(PixelData pixelData, int x, int y, double value)
    {
        if (x < 0 || x >= pixelData.Width || y < 0 || y >= pixelData.Height || pixelData.Data == null)
            return;

        var index = y * pixelData.Width + x;
        var clampedValue = Math.Max(0, Math.Min(1, value));

        if (pixelData.Data is byte[] byteData && index < byteData.Length)
        {
            byteData[index] = (byte)(clampedValue * 255);
        }
        else if (pixelData.Data is ushort[] ushortData && index < ushortData.Length)
        {
            ushortData[index] = (ushort)(clampedValue * 65535);
        }
    }

    #endregion
}

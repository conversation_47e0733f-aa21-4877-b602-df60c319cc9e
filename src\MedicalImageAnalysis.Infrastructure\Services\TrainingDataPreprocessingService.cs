using MedicalImageAnalysis.Core.Entities;
using MedicalImageAnalysis.Core.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace MedicalImageAnalysis.Infrastructure.Services;

/// <summary>
/// 训练数据预处理服务
/// </summary>
public class TrainingDataPreprocessingService
{
    private readonly ILogger<TrainingDataPreprocessingService> _logger;
    private readonly IImageProcessingService _imageProcessingService;
    private readonly IAdvancedImageProcessingService _advancedImageProcessingService;

    public TrainingDataPreprocessingService(
        ILogger<TrainingDataPreprocessingService> logger,
        IImageProcessingService imageProcessingService,
        IAdvancedImageProcessingService advancedImageProcessingService)
    {
        _logger = logger;
        _imageProcessingService = imageProcessingService;
        _advancedImageProcessingService = advancedImageProcessingService;
    }

    /// <summary>
    /// 预处理训练数据集
    /// </summary>
    public async Task<DatasetPreprocessingResult> PreprocessDatasetAsync(
        DatasetPreprocessingConfig config,
        IProgress<DatasetPreprocessingProgress>? progressCallback = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("开始预处理训练数据集: {DatasetPath}", config.DatasetPath);

        var result = new DatasetPreprocessingResult
        {
            StartTime = DateTime.UtcNow,
            Config = config
        };

        try
        {
            // 1. 验证数据集结构
            await ValidateDatasetStructureAsync(config, progressCallback, cancellationToken);

            // 2. 图像预处理
            var processedImages = await ProcessImagesAsync(config, progressCallback, cancellationToken);
            result.ProcessedImageCount = processedImages.Count;

            // 3. 标注预处理
            var processedAnnotations = await ProcessAnnotationsAsync(config, processedImages, progressCallback, cancellationToken);
            result.ProcessedAnnotationCount = processedAnnotations.Count;

            // 4. 数据增强
            if (config.EnableDataAugmentation)
            {
                var augmentedData = await ApplyDataAugmentationAsync(config, processedImages, processedAnnotations, progressCallback, cancellationToken);
                result.AugmentedImageCount = augmentedData.images.Count;
                result.AugmentedAnnotationCount = augmentedData.annotations.Count;
            }

            // 5. 数据集分割
            var splitResult = await SplitDatasetAsync(config, processedImages, processedAnnotations, progressCallback, cancellationToken);
            result.TrainImageCount = splitResult.trainImages.Count;
            result.ValidationImageCount = splitResult.validationImages.Count;
            result.TestImageCount = splitResult.testImages.Count;

            // 6. 生成配置文件
            await GenerateConfigurationFilesAsync(config, splitResult, progressCallback, cancellationToken);

            result.Success = true;
            result.EndTime = DateTime.UtcNow;
            result.ProcessingTimeMs = (long)(result.EndTime.Value - result.StartTime).TotalMilliseconds;

            _logger.LogInformation("数据集预处理完成，耗时: {ProcessingTime}ms", result.ProcessingTimeMs);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据集预处理失败");
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.EndTime = DateTime.UtcNow;
            return result;
        }
    }

    private async Task ValidateDatasetStructureAsync(
        DatasetPreprocessingConfig config,
        IProgress<DatasetPreprocessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("验证数据集结构");

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "验证数据集结构",
            Progress = 0,
            Message = "检查数据集目录结构..."
        });

        await Task.Run(() =>
        {
            if (!Directory.Exists(config.DatasetPath))
            {
                throw new DirectoryNotFoundException($"数据集目录不存在: {config.DatasetPath}");
            }

            var imageFiles = Directory.GetFiles(config.DatasetPath, "*.*", SearchOption.AllDirectories)
                .Where(f => config.SupportedImageFormats.Any(ext => f.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
                .ToList();

            if (imageFiles.Count == 0)
            {
                throw new InvalidOperationException("数据集中未找到支持的图像文件");
            }

            _logger.LogInformation("找到 {ImageCount} 个图像文件", imageFiles.Count);
        }, cancellationToken);

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "验证数据集结构",
            Progress = 100,
            Message = $"验证完成"
        });
    }

    private async Task<List<ProcessedImage>> ProcessImagesAsync(
        DatasetPreprocessingConfig config,
        IProgress<DatasetPreprocessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始图像预处理");

        var imageFiles = Directory.GetFiles(config.DatasetPath, "*.*", SearchOption.AllDirectories)
            .Where(f => config.SupportedImageFormats.Any(ext => f.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
            .ToList();

        var processedImages = new List<ProcessedImage>();
        var totalImages = imageFiles.Count;

        for (int i = 0; i < imageFiles.Count; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var imageFile = imageFiles[i];
            var progress = (int)((double)(i + 1) / totalImages * 100);

            progressCallback?.Report(new DatasetPreprocessingProgress
            {
                Stage = "图像预处理",
                Progress = progress,
                Message = $"处理图像 {i + 1}/{totalImages}: {Path.GetFileName(imageFile)}"
            });

            try
            {
                var processedImage = await ProcessSingleImageAsync(imageFile, config, cancellationToken);
                processedImages.Add(processedImage);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "处理图像失败: {ImageFile}", imageFile);
            }
        }

        _logger.LogInformation("图像预处理完成，成功处理 {ProcessedCount}/{TotalCount} 个图像", 
            processedImages.Count, totalImages);

        return processedImages;
    }

    private async Task<ProcessedImage> ProcessSingleImageAsync(
        string imagePath,
        DatasetPreprocessingConfig config,
        CancellationToken cancellationToken)
    {
        // 这里实现单个图像的预处理逻辑
        // 包括尺寸调整、归一化、格式转换等

        var processedImage = new ProcessedImage
        {
            OriginalPath = imagePath,
            ProcessedPath = Path.Combine(config.OutputPath, "images", Path.GetFileName(imagePath)),
            Width = config.TargetImageSize,
            Height = config.TargetImageSize,
            ProcessedAt = DateTime.UtcNow
        };

        // 模拟图像处理
        await Task.Delay(10, cancellationToken);

        return processedImage;
    }

    private async Task<List<ProcessedAnnotation>> ProcessAnnotationsAsync(
        DatasetPreprocessingConfig config,
        List<ProcessedImage> processedImages,
        IProgress<DatasetPreprocessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始标注预处理");

        var processedAnnotations = new List<ProcessedAnnotation>();

        // 查找标注文件
        var annotationFiles = Directory.GetFiles(config.DatasetPath, "*.json", SearchOption.AllDirectories)
            .Concat(Directory.GetFiles(config.DatasetPath, "*.xml", SearchOption.AllDirectories))
            .Concat(Directory.GetFiles(config.DatasetPath, "*.txt", SearchOption.AllDirectories))
            .ToList();

        var totalAnnotations = annotationFiles.Count;

        for (int i = 0; i < annotationFiles.Count; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var annotationFile = annotationFiles[i];
            var progress = (int)((double)(i + 1) / totalAnnotations * 100);

            progressCallback?.Report(new DatasetPreprocessingProgress
            {
                Stage = "标注预处理",
                Progress = progress,
                Message = $"处理标注 {i + 1}/{totalAnnotations}: {Path.GetFileName(annotationFile)}"
            });

            try
            {
                var annotations = await ProcessSingleAnnotationFileAsync(annotationFile, config, cancellationToken);
                processedAnnotations.AddRange(annotations);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "处理标注文件失败: {AnnotationFile}", annotationFile);
            }
        }

        _logger.LogInformation("标注预处理完成，成功处理 {ProcessedCount} 个标注", processedAnnotations.Count);

        return processedAnnotations;
    }

    private async Task<List<ProcessedAnnotation>> ProcessSingleAnnotationFileAsync(
        string annotationPath,
        DatasetPreprocessingConfig config,
        CancellationToken cancellationToken)
    {
        // 这里实现单个标注文件的预处理逻辑
        // 包括格式转换、坐标归一化等

        var processedAnnotations = new List<ProcessedAnnotation>();

        // 模拟标注处理
        await Task.Delay(5, cancellationToken);

        processedAnnotations.Add(new ProcessedAnnotation
        {
            OriginalPath = annotationPath,
            ProcessedPath = Path.Combine(config.OutputPath, "annotations", Path.GetFileName(annotationPath)),
            AnnotationCount = 1,
            ProcessedAt = DateTime.UtcNow
        });

        return processedAnnotations;
    }

    private async Task<(List<ProcessedImage> images, List<ProcessedAnnotation> annotations)> ApplyDataAugmentationAsync(
        DatasetPreprocessingConfig config,
        List<ProcessedImage> originalImages,
        List<ProcessedAnnotation> originalAnnotations,
        IProgress<DatasetPreprocessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始数据增强");

        var augmentedImages = new List<ProcessedImage>();
        var augmentedAnnotations = new List<ProcessedAnnotation>();

        // 实现数据增强逻辑
        // 包括旋转、翻转、缩放、亮度调整等

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "数据增强",
            Progress = 50,
            Message = "应用数据增强变换..."
        });

        // 模拟数据增强
        await Task.Delay(100, cancellationToken);

        // 为每个原始图像生成增强版本
        foreach (var originalImage in originalImages.Take(Math.Min(originalImages.Count, config.MaxAugmentationCount)))
        {
            for (int i = 0; i < config.AugmentationMultiplier; i++)
            {
                augmentedImages.Add(new ProcessedImage
                {
                    OriginalPath = originalImage.OriginalPath,
                    ProcessedPath = $"{originalImage.ProcessedPath}_aug_{i}",
                    Width = originalImage.Width,
                    Height = originalImage.Height,
                    ProcessedAt = DateTime.UtcNow
                });
            }
        }

        _logger.LogInformation("数据增强完成，生成 {AugmentedCount} 个增强图像", augmentedImages.Count);

        return (augmentedImages, augmentedAnnotations);
    }

    private async Task<DatasetSplitResult> SplitDatasetAsync(
        DatasetPreprocessingConfig config,
        List<ProcessedImage> processedImages,
        List<ProcessedAnnotation> processedAnnotations,
        IProgress<DatasetPreprocessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("开始数据集分割");

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "数据集分割",
            Progress = 0,
            Message = "分割训练、验证和测试集..."
        });

        var result = await Task.Run(() =>
        {
            var totalImages = processedImages.Count;
            var trainCount = (int)(totalImages * config.TrainRatio);
            var validationCount = (int)(totalImages * config.ValidationRatio);
            var testCount = totalImages - trainCount - validationCount;

            // 随机打乱数据
            var random = new Random(config.RandomSeed);
            var shuffledImages = processedImages.OrderBy(x => random.Next()).ToList();

            var trainImages = shuffledImages.Take(trainCount).ToList();
            var validationImages = shuffledImages.Skip(trainCount).Take(validationCount).ToList();
            var testImages = shuffledImages.Skip(trainCount + validationCount).Take(testCount).ToList();

            _logger.LogInformation("数据集分割完成 - 训练集: {TrainCount}, 验证集: {ValidationCount}, 测试集: {TestCount}",
                trainImages.Count, validationImages.Count, testImages.Count);

            return new DatasetSplitResult
            {
                trainImages = trainImages,
                validationImages = validationImages,
                testImages = testImages,
                trainAnnotations = new List<ProcessedAnnotation>(),
                validationAnnotations = new List<ProcessedAnnotation>(),
                testAnnotations = new List<ProcessedAnnotation>()
            };
        }, cancellationToken);

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "数据集分割",
            Progress = 100,
            Message = $"分割完成 - 训练:{result.trainImages.Count}, 验证:{result.validationImages.Count}, 测试:{result.testImages.Count}"
        });

        return result;
    }

    private async Task GenerateConfigurationFilesAsync(
        DatasetPreprocessingConfig config,
        DatasetSplitResult splitResult,
        IProgress<DatasetPreprocessingProgress>? progressCallback,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("生成配置文件");

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "生成配置文件",
            Progress = 0,
            Message = "生成训练配置文件..."
        });

        // 生成YOLO格式的数据集配置文件
        var datasetConfig = new
        {
            path = config.OutputPath,
            train = "images/train",
            val = "images/val",
            test = "images/test",
            names = config.ClassNames.Select((name, index) => new { index, name }).ToDictionary(x => x.index, x => x.name)
        };

        var configPath = Path.Combine(config.OutputPath, "dataset.yaml");
        var configJson = JsonSerializer.Serialize(datasetConfig, new JsonSerializerOptions { WriteIndented = true });
        await File.WriteAllTextAsync(configPath, configJson, cancellationToken);

        // 生成训练统计信息
        var statistics = new
        {
            total_images = splitResult.trainImages.Count + splitResult.validationImages.Count + splitResult.testImages.Count,
            train_images = splitResult.trainImages.Count,
            validation_images = splitResult.validationImages.Count,
            test_images = splitResult.testImages.Count,
            classes = config.ClassNames,
            image_size = config.TargetImageSize,
            preprocessing_config = config,
            generated_at = DateTime.UtcNow
        };

        var statisticsPath = Path.Combine(config.OutputPath, "dataset_statistics.json");
        var statisticsJson = JsonSerializer.Serialize(statistics, new JsonSerializerOptions { WriteIndented = true });
        await File.WriteAllTextAsync(statisticsPath, statisticsJson, cancellationToken);

        _logger.LogInformation("配置文件生成完成");

        progressCallback?.Report(new DatasetPreprocessingProgress
        {
            Stage = "生成配置文件",
            Progress = 100,
            Message = "配置文件生成完成"
        });
    }
}

/// <summary>
/// 数据集预处理配置
/// </summary>
public class DatasetPreprocessingConfig
{
    public string DatasetPath { get; set; } = string.Empty;
    public string OutputPath { get; set; } = string.Empty;
    public int TargetImageSize { get; set; } = 640;
    public List<string> SupportedImageFormats { get; set; } = new() { ".jpg", ".jpeg", ".png", ".bmp", ".tiff" };
    public List<string> ClassNames { get; set; } = new();
    public bool EnableDataAugmentation { get; set; } = true;
    public int AugmentationMultiplier { get; set; } = 3;
    public int MaxAugmentationCount { get; set; } = 1000;
    public double TrainRatio { get; set; } = 0.7;
    public double ValidationRatio { get; set; } = 0.2;
    public double TestRatio { get; set; } = 0.1;
    public int RandomSeed { get; set; } = 42;
    public bool NormalizeImages { get; set; } = true;
    public bool ResizeImages { get; set; } = true;
}

/// <summary>
/// 数据集预处理结果
/// </summary>
public class DatasetPreprocessingResult
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public long ProcessingTimeMs { get; set; }
    public DatasetPreprocessingConfig Config { get; set; } = new();
    public int ProcessedImageCount { get; set; }
    public int ProcessedAnnotationCount { get; set; }
    public int AugmentedImageCount { get; set; }
    public int AugmentedAnnotationCount { get; set; }
    public int TrainImageCount { get; set; }
    public int ValidationImageCount { get; set; }
    public int TestImageCount { get; set; }
}

/// <summary>
/// 数据集预处理进度
/// </summary>
public class DatasetPreprocessingProgress
{
    public string Stage { get; set; } = string.Empty;
    public int Progress { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// 处理后的图像信息
/// </summary>
public class ProcessedImage
{
    public string OriginalPath { get; set; } = string.Empty;
    public string ProcessedPath { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
    public DateTime ProcessedAt { get; set; }
}

/// <summary>
/// 处理后的标注信息
/// </summary>
public class ProcessedAnnotation
{
    public string OriginalPath { get; set; } = string.Empty;
    public string ProcessedPath { get; set; } = string.Empty;
    public int AnnotationCount { get; set; }
    public DateTime ProcessedAt { get; set; }
}

/// <summary>
/// 数据集分割结果
/// </summary>
public class DatasetSplitResult
{
    public List<ProcessedImage> trainImages { get; set; } = new();
    public List<ProcessedImage> validationImages { get; set; } = new();
    public List<ProcessedImage> testImages { get; set; } = new();
    public List<ProcessedAnnotation> trainAnnotations { get; set; } = new();
    public List<ProcessedAnnotation> validationAnnotations { get; set; } = new();
    public List<ProcessedAnnotation> testAnnotations { get; set; } = new();
}
